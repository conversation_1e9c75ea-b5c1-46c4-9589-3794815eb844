/**
 * Authentication Page Component
 * Menggunakan ATMA API melalui authService yang sudah dikonfigurasi
 * Base URL: https://api.chhrone.web.id
 * Documentation: https://docs.chhrone.web.id
 */

// Import auth service yang sudah dikonfigurasi
import { authService } from '../services/authService.js';

let isLoginMode = true;

/**
 * Creates the authentication page HTML structure
 * Includes both login and registration forms with proper validation
 * Following the styling from Login.jsx and Register.jsx templates
 */
export function createAuthPage() {
  return `
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div class="glass-card-light p-8 rounded-2xl shadow-2xl">
          <!-- Header with Logo -->
          <div class="space-y-1 mb-8">
            <div class="flex justify-center">
              <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-xl">A</span>
              </div>
            </div>
          </div>

          <!-- Auth Title and Subtitle -->
          <div class="text-center mb-8">
            <h2 id="auth-title" class="text-2xl font-bold text-gray-900 mb-2">
              Masuk ke Akun Anda
            </h2>
            <p id="auth-subtitle" class="text-sm text-gray-600">
              Atau
              <a href="#" class="font-medium text-blue-600 hover:text-blue-500" onclick="toggleAuthMode()">
                daftar akun baru
              </a>
            </p>
          </div>

          <!-- Error/Success Messages -->
          <div id="auth-message" class="hidden mb-6 p-4 rounded-lg">
            <p id="auth-message-text" class="text-sm"></p>
          </div>

          <!-- Login Form -->
          <form id="login-form" class="space-y-6" onsubmit="handleLogin(event)">
            <div>
              <label for="login-email" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                id="login-email"
                name="email"
                type="email"
                autocomplete="email"
                required
                maxlength="255"
                placeholder="Masukkan email Anda"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
            </div>

            <div>
              <label for="login-password" class="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                id="login-password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                placeholder="Masukkan password Anda"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                  Ingat saya
                </label>
              </div>

              <div class="text-sm">
                <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                  Lupa password?
                </a>
              </div>
            </div>

            <div>
              <button
                type="submit"
                id="login-button"
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                <span id="login-button-text">Masuk</span>
                <svg id="login-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </button>
            </div>
          </form>

          <!-- Register Form -->
          <form id="register-form" class="space-y-6 hidden" onsubmit="handleRegister(event)">
            <div>
              <label for="register-email" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                id="register-email"
                name="email"
                type="email"
                autocomplete="email"
                required
                maxlength="255"
                placeholder="Masukkan email Anda"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
              <p class="mt-2 text-xs text-gray-500">Maksimal 255 karakter</p>
            </div>

            <div>
              <label for="register-password" class="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                id="register-password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                minlength="8"
                pattern="^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$"
                placeholder="Minimal 8 karakter dengan huruf dan angka"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
              <p class="mt-2 text-xs text-gray-500">Minimal 8 karakter, harus mengandung huruf dan angka</p>
            </div>

            <div>
              <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                Konfirmasi Password
              </label>
              <input
                id="confirm-password"
                name="confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                placeholder="Ulangi password Anda"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
            </div>

            <div>
              <button
                type="submit"
                id="register-button"
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                <span id="register-button-text">Daftar</span>
                <svg id="register-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </button>
            </div>
          </form>

          <!-- API Info -->
          <div class="mt-8 text-center">
            <p class="text-xs text-gray-400">
              Powered by ATMA API v1.0.0 • Rate Limited
            </p>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Toggle between login and register forms
 * Updates UI elements and form visibility
 */
export function toggleAuthMode() {
  try {
    isLoginMode = !isLoginMode;

    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      const loginForm = document.getElementById('login-form');
      const registerForm = document.getElementById('register-form');
      const title = document.getElementById('auth-title');
      const subtitle = document.getElementById('auth-subtitle');

      // Validate DOM elements exist
      if (!loginForm || !registerForm || !title || !subtitle) {
        console.error('Auth form elements not found');
        return;
      }

      if (isLoginMode) {
        // Show login form, hide register form
        loginForm.style.setProperty('display', 'block', 'important');
        registerForm.style.setProperty('display', 'none', 'important');
        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');

        title.textContent = 'Masuk ke Akun Anda';
        subtitle.innerHTML = `
          Atau
          <a href="#" class="font-medium text-blue-600 hover:text-blue-500" onclick="toggleAuthMode()">
            daftar akun baru
          </a>
        `;
      } else {
        // Show register form, hide login form
        loginForm.style.setProperty('display', 'none', 'important');
        registerForm.style.setProperty('display', 'block', 'important');
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');

        title.textContent = 'Daftar Akun Baru';
        subtitle.innerHTML = `
          Sudah punya akun?
          <a href="#" class="font-medium text-blue-600 hover:text-blue-500" onclick="toggleAuthMode()">
            masuk di sini
          </a>
        `;
      }

      // Clear any previous messages and reset forms
      hideMessage();
      clearForms();

      console.log('Auth mode toggled to:', isLoginMode ? 'login' : 'register');

      // Debug form visibility
      debugFormVisibility();
    }, 10); // Small delay to ensure DOM is ready
  } catch (error) {
    console.error('Error in toggleAuthMode:', error);
    showMessage('Terjadi kesalahan saat mengganti mode. Silakan refresh halaman.', true);
  }
}

/**
 * Clear all form inputs
 */
function clearForms() {
  const forms = ['login-form', 'register-form'];
  forms.forEach(formId => {
    const form = document.getElementById(formId);
    if (form) {
      form.reset();
    }
  });
}

/**
 * Display success or error message to user
 * @param {string} message - Message to display
 * @param {boolean} isError - Whether this is an error message
 */
function showMessage(message, isError = false) {
  const messageDiv = document.getElementById('auth-message');
  const messageText = document.getElementById('auth-message-text');

  if (!messageDiv || !messageText) {
    console.error('Message elements not found');
    return;
  }

  messageDiv.className = `mb-4 p-3 rounded-md ${
    isError
      ? 'bg-red-50 border border-red-200'
      : 'bg-green-50 border border-green-200'
  }`;

  messageText.className = `text-sm ${
    isError ? 'text-red-700' : 'text-green-700'
  }`;

  messageText.textContent = message;
  messageDiv.classList.remove('hidden');

  // Auto-hide success messages after 5 seconds
  if (!isError) {
    setTimeout(() => {
      hideMessage();
    }, 5000);
  }
}

/**
 * Hide message display
 */
function hideMessage() {
  const messageDiv = document.getElementById('auth-message');
  if (messageDiv) {
    messageDiv.classList.add('hidden');
  }
}

/**
 * Set loading state for form buttons
 * @param {string} formType - 'login' or 'register'
 * @param {boolean} isLoading - Whether to show loading state
 */
function setLoadingState(formType, isLoading) {
  const button = document.getElementById(`${formType}-button`);
  const buttonText = document.getElementById(`${formType}-button-text`);
  const spinner = document.getElementById(`${formType}-spinner`);

  if (!button || !buttonText || !spinner) {
    console.error(`Loading state elements not found for ${formType}`);
    return;
  }

  if (isLoading) {
    button.disabled = true;
    buttonText.textContent = formType === 'login' ? 'Masuk...' : 'Mendaftar...';
    spinner.classList.remove('hidden');
  } else {
    button.disabled = false;
    buttonText.textContent = formType === 'login' ? 'Masuk' : 'Daftar';
    spinner.classList.add('hidden');
  }
}

/**
 * Handle login form submission
 * Uses authService.login() method with proper error handling
 */
export async function handleLogin(event) {
  event.preventDefault();
  hideMessage();
  setLoadingState('login', true);

  const formData = new FormData(event.target);
  const email = formData.get('email')?.trim();
  const password = formData.get('password');

  // Client-side validation
  if (!email || !password) {
    showMessage('Email dan password harus diisi.', true);
    setLoadingState('login', false);
    return;
  }

  try {
    console.log('Attempting login with ATMA API...');
    const result = await authService.login(email, password);

    if (result.success) {
      showMessage('Login berhasil! Mengalihkan ke dashboard...', false);

      // Log successful authentication
      console.log('Login successful:', {
        user: result.data?.user,
        hasToken: !!result.data?.token
      });

      // Redirect to dashboard after short delay
      setTimeout(() => {
        window.location.hash = '#dashboard';
      }, 1500);
    } else {
      // Handle API errors with user-friendly messages
      const errorMessage = result.error || 'Login gagal. Silakan periksa email dan password Anda.';
      showMessage(errorMessage, true);
      console.error('Login failed:', result.error);
    }
  } catch (error) {
    showMessage('Terjadi kesalahan koneksi. Silakan coba lagi.', true);
    console.error('Login error:', error);
  } finally {
    setLoadingState('login', false);
  }
}

/**
 * Handle register form submission
 * Uses authService.register() method with proper validation and error handling
 */
export async function handleRegister(event) {
  event.preventDefault();
  hideMessage();
  setLoadingState('register', true);

  const formData = new FormData(event.target);
  const email = formData.get('email')?.trim();
  const password = formData.get('password');
  const confirmPassword = formData.get('confirmPassword');

  // Client-side validation
  if (!email || !password || !confirmPassword) {
    showMessage('Semua field harus diisi.', true);
    setLoadingState('register', false);
    return;
  }

  // Validate email format using authService validation
  if (!authService.validateEmail(email)) {
    showMessage('Format email tidak valid atau terlalu panjang (maksimal 255 karakter).', true);
    setLoadingState('register', false);
    return;
  }

  // Validate password format using authService validation
  if (!authService.validatePassword(password)) {
    showMessage('Password harus minimal 8 karakter dan mengandung huruf serta angka.', true);
    setLoadingState('register', false);
    return;
  }

  // Validate password confirmation
  if (password !== confirmPassword) {
    showMessage('Password dan konfirmasi password tidak cocok.', true);
    setLoadingState('register', false);
    return;
  }

  try {
    console.log('Attempting registration with ATMA API...');
    const result = await authService.register(email, password);

    if (result.success) {
      showMessage('Registrasi berhasil! Mengalihkan ke dashboard...', false);

      // Log successful registration
      console.log('Registration successful:', {
        user: result.data?.user,
        hasToken: !!result.data?.token
      });

      // Redirect to dashboard after short delay
      setTimeout(() => {
        window.location.hash = '#dashboard';
      }, 1500);
    } else {
      // Handle API errors with user-friendly messages
      const errorMessage = result.error || 'Registrasi gagal. Silakan coba lagi.';
      showMessage(errorMessage, true);
      console.error('Registration failed:', result.error);
    }
  } catch (error) {
    showMessage('Terjadi kesalahan koneksi. Silakan coba lagi.', true);
    console.error('Registration error:', error);
  } finally {
    setLoadingState('register', false);
  }
}

/**
 * Legacy function for backward compatibility
 */
export function showRegister() {
  if (isLoginMode) {
    toggleAuthMode();
  }
}

/**
 * Check if user is already authenticated and redirect if needed
 */
export function checkAuthStatus() {
  if (authService.isAuthenticated()) {
    console.log('User already authenticated, redirecting to dashboard...');
    window.location.hash = '#dashboard';
    return true;
  }
  return false;
}

/**
 * Initialize auth page
 * Sets up event listeners and checks authentication status
 */
export function initAuthPage() {
  // Check if user is already logged in
  if (checkAuthStatus()) {
    return;
  }

  // Ensure initial state is correct
  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');

  if (loginForm && registerForm) {
    // Force initial state with !important
    loginForm.style.setProperty('display', 'block', 'important');
    registerForm.style.setProperty('display', 'none', 'important');
    loginForm.classList.remove('hidden');
    registerForm.classList.add('hidden');

    console.log('Initial form state set:', {
      loginVisible: loginForm.style.display,
      registerVisible: registerForm.style.display
    });
  }

  // Add real-time password validation for register form
  const registerPassword = document.getElementById('register-password');
  const confirmPassword = document.getElementById('confirm-password');

  if (registerPassword) {
    registerPassword.addEventListener('input', validatePasswordRealTime);
  }

  if (confirmPassword) {
    confirmPassword.addEventListener('input', validatePasswordMatch);
  }

  console.log('Auth page initialized with ATMA API integration');
}

/**
 * Debug function to check form visibility
 */
function debugFormVisibility() {
  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');

  console.log('Form visibility debug:', {
    loginForm: {
      exists: !!loginForm,
      display: loginForm?.style.display,
      hasHiddenClass: loginForm?.classList.contains('hidden'),
      computedDisplay: loginForm ? window.getComputedStyle(loginForm).display : 'N/A'
    },
    registerForm: {
      exists: !!registerForm,
      display: registerForm?.style.display,
      hasHiddenClass: registerForm?.classList.contains('hidden'),
      computedDisplay: registerForm ? window.getComputedStyle(registerForm).display : 'N/A'
    }
  });
}

/**
 * Real-time password validation
 */
function validatePasswordRealTime(event) {
  const password = event.target.value;
  const isValid = authService.validatePassword(password);

  if (password.length > 0 && !isValid) {
    event.target.setCustomValidity('Password harus minimal 8 karakter dan mengandung huruf serta angka');
  } else {
    event.target.setCustomValidity('');
  }
}

/**
 * Real-time password match validation
 */
function validatePasswordMatch(event) {
  const confirmPassword = event.target.value;
  const password = document.getElementById('register-password')?.value;

  if (confirmPassword.length > 0 && password !== confirmPassword) {
    event.target.setCustomValidity('Password tidak cocok');
  } else {
    event.target.setCustomValidity('');
  }
}

// Make functions globally available for onclick handlers
window.toggleAuthMode = toggleAuthMode;
window.handleLogin = handleLogin;
window.handleRegister = handleRegister;
window.showRegister = showRegister;
window.debugFormVisibility = debugFormVisibility;

// Debug: Log when functions are attached to window
console.log('Auth functions attached to window:', {
  toggleAuthMode: typeof window.toggleAuthMode,
  handleLogin: typeof window.handleLogin,
  handleRegister: typeof window.handleRegister,
  showRegister: typeof window.showRegister,
  debugFormVisibility: typeof window.debugFormVisibility
});

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initAuthPage);
} else {
  initAuthPage();
}