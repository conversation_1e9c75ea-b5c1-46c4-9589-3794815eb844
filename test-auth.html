<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auth Toggle</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto">
        <h1 class="text-2xl font-bold mb-4">Test Auth Toggle</h1>
        
        <div id="auth-title" class="text-xl font-semibold mb-2">Masuk ke Akun Anda</div>
        <div id="auth-subtitle" class="mb-4">
            Atau
            <a href="#" class="text-blue-600 hover:text-blue-800" onclick="toggleAuthMode()">
                daftar akun baru
            </a>
        </div>

        <!-- Login Form -->
        <form id="login-form" class="space-y-4 p-4 border border-gray-300 rounded">
            <h2 class="font-semibold">Login Form</h2>
            <input type="email" placeholder="Email" class="w-full p-2 border rounded">
            <input type="password" placeholder="Password" class="w-full p-2 border rounded">
            <button type="submit" class="w-full bg-blue-600 text-white p-2 rounded">Login</button>
        </form>

        <!-- Register Form -->
        <form id="register-form" class="space-y-4 p-4 border border-gray-300 rounded hidden">
            <h2 class="font-semibold">Register Form</h2>
            <input type="email" placeholder="Email" class="w-full p-2 border rounded">
            <input type="password" placeholder="Password" class="w-full p-2 border rounded">
            <input type="password" placeholder="Confirm Password" class="w-full p-2 border rounded">
            <button type="submit" class="w-full bg-green-600 text-white p-2 rounded">Register</button>
        </form>

        <div class="mt-4">
            <button onclick="testToggle()" class="bg-gray-600 text-white px-4 py-2 rounded">
                Test Toggle Function
            </button>
        </div>

        <div id="debug-info" class="mt-4 p-4 bg-gray-200 rounded text-sm"></div>
    </div>

    <script>
        let isLoginMode = true;

        function toggleAuthMode() {
            try {
                isLoginMode = !isLoginMode;

                const loginForm = document.getElementById('login-form');
                const registerForm = document.getElementById('register-form');
                const title = document.getElementById('auth-title');
                const subtitle = document.getElementById('auth-subtitle');

                console.log('Toggle called, isLoginMode:', isLoginMode);
                console.log('Elements found:', {
                    loginForm: !!loginForm,
                    registerForm: !!registerForm,
                    title: !!title,
                    subtitle: !!subtitle
                });

                if (!loginForm || !registerForm || !title || !subtitle) {
                    console.error('Auth form elements not found');
                    return;
                }

                if (isLoginMode) {
                    loginForm.style.display = 'block';
                    registerForm.style.display = 'none';
                    loginForm.classList.remove('hidden');
                    registerForm.classList.add('hidden');
                    
                    title.textContent = 'Masuk ke Akun Anda';
                    subtitle.innerHTML = `
                        Atau
                        <a href="#" class="text-blue-600 hover:text-blue-800" onclick="toggleAuthMode()">
                            daftar akun baru
                        </a>
                    `;
                } else {
                    loginForm.style.display = 'none';
                    registerForm.style.display = 'block';
                    loginForm.classList.add('hidden');
                    registerForm.classList.remove('hidden');
                    
                    title.textContent = 'Daftar Akun Baru';
                    subtitle.innerHTML = `
                        Sudah punya akun?
                        <a href="#" class="text-blue-600 hover:text-blue-800" onclick="toggleAuthMode()">
                            masuk di sini
                        </a>
                    `;
                }

                updateDebugInfo();
                console.log('Auth mode toggled to:', isLoginMode ? 'login' : 'register');
            } catch (error) {
                console.error('Error in toggleAuthMode:', error);
            }
        }

        function testToggle() {
            console.log('Testing toggle function...');
            toggleAuthMode();
        }

        function updateDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');
            
            debugDiv.innerHTML = `
                <strong>Debug Info:</strong><br>
                Current Mode: ${isLoginMode ? 'Login' : 'Register'}<br>
                Login Form Display: ${loginForm.style.display}<br>
                Register Form Display: ${registerForm.style.display}<br>
                Login Form Classes: ${loginForm.className}<br>
                Register Form Classes: ${registerForm.className}
            `;
        }

        // Initial debug info
        updateDebugInfo();
    </script>
</body>
</html>
